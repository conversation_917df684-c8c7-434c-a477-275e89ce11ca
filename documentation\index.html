<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>Shared on THEMELOCK.COM - Cigma - Multipurpose Business Consulting HTML Templates</title>
        <!-- Stylesheets -->
        <link href="css/bootstrap.css" rel="stylesheet" />
        <link href="css/style.css" rel="stylesheet" />
        <link href="css/scrollbar.css" rel="stylesheet" />
        <!-- Responsive -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
        />
        <!--[if lt IE 9
            ]><script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script
        ><![endif]-->
        <!--[if lt IE 9]><script src="js/respond.js"></script><![endif]-->
    </head>

    <body>
        <!--Page Wrapper-->
        <div class="page-wrapper">
            <!--Sidebar-->
            <aside id="sidebar">
                <!--Toggle Button-->
                <div class="button-container clearfix">
                    <div class="menu-toggle flaticon-menu10"></div>
                </div>

                <!--Menu Box-->
                <div class="menu-box">
                    <!--Logo-->
                    <br />
                    <div class="logo text-left">
                        <a href="#"><img src="images/logo-white.svg" alt="img"></a>
                    </div>

                    <!--Sticky Menu-->
                    <nav class="sticky-menu">
                        <ul>
                            <li class="current">
                                <a href="#introduction"
                                    ><span class="fa fa-arrow-right"></span
                                        >Introduction </a>
                            </li>
                            <li>
                                <a href="#html-structure"
                                    >HTML Structure <span class="fa fa-arrow-right"></span
                                ></a>
                            </li>
                            <li>
                                <a href="#css-structure"
                                    >CSS Files and Structure <span class="fa fa-arrow-right"></span
                                ></a>
                            </li>
                            <li>
                                <a href="#javascript"
                                    >Javascript <span class="fa fa-arrow-right"></span
                                ></a>
                            </li>
                            <li>
                                <a href="#contact-form"
                                    >Contact Form Settings<span class="fa fa-arrow-right"></span
                                ></a>
                            </li>
                            <li>
                                <a href="#required-file"
                                    >Required File<span class="fa fa-arrow-right"></span
                                ></a>
                            </li>
                            <li>
                                <a href="#logo-area"
                                    >Logo<span class="fa fa-arrow-right"></span
                                ></a>
                            </li>
                            <li>
                                <a href="#blog-area"
                                    >blog Area<span class="fa fa-arrow-right"></span
                                ></a>
                            </li>
                            <li>
                                <a href="#footer-area"
                                    >Footer & Widget<span class="fa fa-arrow-right"></span
                                ></a>
                            </li>
                            <li>
                                <a href="#copyright-area"
                                    >Copy Right<span class="fa fa-arrow-right"></span
                                ></a>
                            </li>
                            <li>
                                <a href="#Feature-card"
                                    >Features <span class="fa fa-arrow-right"></span
                                ></a>
                            </li>
                            <li>
                                <a href="#sources-credits"
                                    >Sources and Credits <span class="fa fa-arrow-right"></span
                                ></a>
                            </li>
                            <li>
                                <a href="#support"
                                    >Support <span class="fa fa-arrow-right"></span
                                ></a>
                            </li>
                        </ul>
                    </nav>
                </div>

                <div class="copyright">Copyright &copy; <strong><a href="https://themeforest.net/user/themehour">themehour</a></strong> 2025</div>
            </aside>

            <div id="content-section">
                <!--Introduction Section-->
                <section class="section introduction" id="introduction">
                    <div class="sec-title">
                        <h2>Introduction</h2>
                    </div>

                    <br />
                    <div class="sec-content">
                        <div class="logo">
                            <img src="images/logo.svg" alt="icon">
                        </div>
                        <h3>Business Agency HTML Template</h3>

                        <div class="separator"></div>

                        <strong>Created :</strong> 04/01/2025<br />
                        <strong>Last Update :</strong> 04/01/2025<br />
                        <strong>By :</strong> <a href="https://themeforest.net/user/themehour">themehour</a> <br />
                        <br />
                        <p>
                            Thank you for purchasing <strong>Cigma</strong> - Business Agency HTML Template. 
                            If you have any questions that are beyond the scope of
                            this help file, please feel free to email, via my user page contact form
                            or put a ticket at <strong><a href="mailto:<EMAIL>">Support Center</a></strong>.<br />
                        </p>
                    </div>
                </section>

                <!--HTML Structure-->
                <section class="section html-structure" id="html-structure">
                    <div class="sec-title">
                        <h2>HTML Structure</h2>
                    </div>

                    <br />
                    <div class="sec-content">
                        <h3>
                            The html template uses Latest <strong>Bootstrap 5</strong> with valid
                            HTML5 tags. This theme is a responisve layout with 4 column Support
                            column. All of the information in content area is nested within a class
                            and comes with predefined classes.
                        </h3>

                        <div class="html-code">
                            <img src="images/html-sample.png" alt="image html"/>
                        </div>
                        <br>
                        <br>
                        <div class="html-code">
                            <h2>Output</h2>
                            <br> 
                            <img src="images/output.png" alt="image html"/>
                        </div>
                    </div>
                </section>

                <!--CSS Structure-->
                <section class="section css-structure" id="css-structure">
                    <div class="sec-title">
                        <h2>SASS Files and Structure</h2>
                    </div>

                    <br />
                    <div class="sec-content">
                        <h3>
                            Mainly three main CSS files are used in this theme. The first one
                            <strong>bootstrap.css</strong>, second one is
                            <strong>style.css</strong> which for this template and third one is
                            <strong>responsive.css</strong> to control responsive layouts.
                        </h3>
                        <p>
                            If you would like to edit a specific section of the site, simply find
                            the appropriate label in the CSS file, and then scroll down until you
                            find the appropriate style that needs to be edited.
                        </p>
                        <h2>We used SASS for better productivity</h2>
                        <p>
                            Instead of working with large css file we break them into three pieces
                        </p>
                        <p>
                            <strong>Elements:</strong> We keep every modular elements style in this
                            folder. Module files are name according to their class name. For naming
                            convention we used BEM method.
                        </p>
                        <p><strong>General:</strong> General styles are written here.</p>
                        <p><strong>Sections:</strong> Section specific styles are written here.</p>

                        <div class="html-code">
                            <br> 
                            <img src="images/css.png" alt="image html"/>
                        </div>
                    </div>
                </section>

                <!--Javascript-->
                <section class="section javascript" id="javascript">
                    <div class="sec-title">
                        <h2>Jquery and javascript</h2>
                    </div>

                    <br />
                    <div class="sec-content">
                        <h3>This theme imports three types of Javascript files.</h3>
                        <ul>
                            <li>jQuery</li>
                            <li>Custom scripts <strong>(main.js)</strong></li>
                            <li>Scripts for google map <strong>(main.js)</strong></li>
                            <li>Use Some plugins file</li>
                            <br />
                            <li>
                                <strong>jQuery</strong> is a Javascript library that greatly reduces
                                the amount of code that you must write.
                            </li>
                            <li>
                                Most of the animation in this site is carried out from the customs
                                scripts. There are a few functions worth looking over.
                            </li>
                        </ul>
                        <br />
                        <p>
                            In addition to the custom scripts, I have implemented few "tried and
                            true" plugins to create the effects. This plugin is packed, so you won't
                            need to manually edit anything in the file. The only necessary thing to
                            know is how to call the method
                        </p>

                        <div class="html-code">
                            <br> 
                            <img src="images/js.png" alt="image html"/>
                        </div>
                    </div>
                </section>

                <!--contact-form Settings-->
                <section class="section map-settings" id="contact-form">
                    <div class="sec-title">
                        <h2>Contact form Structure</h2>
                    </div>

                    <br />
                    <div class="sec-content">
                        <div class="html-code">
                            <img src="images/contact.png" alt="image html"/>
                        </div>
                        <br>
                        <br>
                        <div class="sec-content">
                            <h2>Output</h2>
                            <br>
                            <div class="html-code">
                                <img src="images/output2.png" alt="image html"/>
                            </div>
                            <br>
                        </div>
                    </div>
                </section>

                <!--required-file-->
                <section class="section map-settings" id="required-file">
                    <div class="sec-title">
                        <h2>Required File</h2>
                    </div>

                    <br />
                    <div class="sec-content">
                        <h2>Required Css</h2>
                        <br>
                        <div class="html-code">
                            <img src="images/required-css.png" alt="image html"/>
                        </div>
                        <br>
                        <br>
                        <div class="html-code">
                            <h2>Required Js</h2>
                            <br> 
                            <img src="images/required-js.png" alt="image html"/>
                        </div>
                    </div>
                </section>

                <!--logo Structure-->
                <section class="section map-settings" id="logo-area">
                    <div class="sec-title">
                        <h2>Logo Structure</h2>
                    </div>

                    <br />
                    <div class="sec-content">
                        <div class="html-code">
                            <img src="images/logo.png" alt="image html"/>
                        </div>
                        <br>
                        <br>
                        <div class="sec-content">
                            <h2>Output</h2>
                            <br>
                            <div class="html-code">
                                <img src="images/output3.png" alt="image html"/>
                            </div>
                            <br>
                        </div>
                    </div>
                </section>

                <!--blog Structure-->
                <section class="section map-settings" id="blog-area">
                    <div class="sec-title">
                        <h2>blog Structure</h2>
                    </div>

                    <br />
                    <div class="sec-content">
                        <div class="html-code">
                            <img src="images/blog.png" alt="image html"/>
                        </div>
                    </div>
                </section>

                <!--Footer Structure-->
                <section class="section map-settings" id="footer-area">
                    <div class="sec-title">
                        <h2>Footer Menu Structure</h2>
                    </div>

                    <br />
                    <div class="sec-content">
                        <div class="html-code">
                            <img src="images/footer.png" alt="image html"/>
                        </div>
                    </div>
                </section>

                <!--Footer Structure-->
                <section class="section map-settings" id="copyright-area">
                    <div class="sec-title">
                        <h2>Copy Right Structure</h2>
                    </div>

                    <br />
                    <div class="sec-content">
                        <div class="html-code">
                            <img src="images/copyright.png" alt="image html"/>
                        </div>
                    </div>
                </section>

                <!--Feature area-->
                <section class="section sources-credits" id="Feature-card">
                    <div class="sec-title">
                        <h2>Features</h2>
                    </div>

                    <br />
                    <div class="sec-content">
                        <ul>
                            <li>SASS File Included</li>
                            <li>100% pixel perfect design</li>
                            <li>Clean and Simple Style</li>
                            <li>Smooth transition effects.</li>
                            <li>Cross Browser Optimization.</li>
                            <li>Google font.</li>
                            <li>Compatible Browsers – IE9+, Chrome, Safari, Opera, Firefox</li>
                            <li>Documentation – Yes</li>
                            <li>License – GPL</li>
                            <li>Version – 1.0.0</li>
                            <li>Columns – 4</li>
                            <li>Files Included -html, JS, css</li>
                            <li>User Friendly Code</li>
                            <li>Easy to customize</li>
                            <li>W3C Validation</li>
                            <li>Clean &amp; Modern Design</li>
                            <li>CSS3 animations</li>
                            <li>Full Responsive</li>
                            <li>SEO friendly code</li>
                        </ul>
                    </div>
					<div class="sec-title">
                        <h2>Pages</h2>
                    </div>
                    <br />
                    <div class="sec-content">
                        <ul>
							<li>Home Corporate</li>
							<li>Home Web Agency</li>
							<li>Home Personal Portfolio</li>
							<li>Home Business Agency</li>
							<li>Home Collaborate</li>
							<li>About</li>
							<li>Project</li>
							<li>Project Details</li>
							<li>Services</li>
							<li>Service Details</li>
							<li>Team Page</li>
							<li>Team Details</li>
							<li>Blog Standard</li>
							<li>Blog Grid</li>
							<li>Blog Details with Sidebar</li>
							<li>Blog Details without Sidebar</li>
							<li>Shop Page</li>
							<li>Shop Details</li>
							<li>Cart Page</li>
							<li>Checkout</li>
							<li>Wishlist</li>
							<li>Contact</li>
							<li>Faq</li>
							<li>Pricing</li>
							<li>Error</li>
						</ul>
                    </div>
					
                </section>

                <!--Sources and Credits-->
                <section class="section sources-credits" id="sources-credits">
                    <div class="sec-title">
                        <h2>Sources and Credits</h2>
                    </div>

                    <br />
                    <div class="sec-content">
                        <h3>
                            Fonts Used int the template are google fonts, you can find theme on
                            Google Fonts API
                        </h3>

                        <p><strong>Font Used:</strong></p>
                        <ul>
                            <li><a href="https://fonts.google.com/specimen/Inter">'Inter'</a></li>
                            <li><a href="https://fonts.google.com/specimen/DM+Sans">'DM Sans'</a></li>
                        </ul>

                        <ul class="source-files">
                            <li><a href="http://www.getbootstrap.com/" target="_blank" title="Bootstrap">Bootstrap</a></li>
                            <li><a href="https://fonts.google.com/" target="_blank" title="Google Fonts">Google Fonts</a></li>
                            <li><a href="https://fontawesome.com">Font Awesome</a></li>
                        </ul>


                        <br>
                        Every Code is properly commented for Editing Ease.
                    </div>
                </section>

                <!--Support-->
                <section class="section support" id="support">
                    <div class="sec-title">
                        <h2>Support</h2>
                    </div>

                    <br />
                    <div class="sec-content">
                        <h3>
                            Once again, Thank you so much for purchasing this theme. As I said at
                            the beginning, I'd be glad to help you if you have any questions
                            relating to this theme. No guarantees, but I'll do my best to assist. If
                            you have any queries, please feel free to contact us at Support Center. <strong><a href="mailto:<EMAIL>"><EMAIL></a></strong>
                        </h3>

                        <p>Email Us at : <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    </div>
                </section>
            </div>
        </div>
        <!--End pagewrapper-->

        <script src="js/jquery.js"></script>
        <script src="js/bootstrap.min.js"></script>
        <script src="js/jquery.nav.js"></script>
        <script src="js/jquery.scrollTo.js"></script>
        <script src="js/scrollbar.js"></script>
        <script src="js/script.js"></script>
    </body>
</html>
